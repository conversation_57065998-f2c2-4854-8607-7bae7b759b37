import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { convertAnonymousUser, convertAnonymousUserWithConflictCheck, transferCertificateToExistingAccount, checkEmailExists } from '../../utils/accountConversion';
import { useCertificate } from '../../contexts/CertificateContext';

interface AccountConversionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  userEmail: string;
}

type ConflictResolutionMode = 'none' | 'email_conflict' | 'login_existing';

export const AccountConversionModal = ({
  isOpen,
  onClose,
  onSuccess,
  userEmail
}: AccountConversionModalProps) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [conflictMode, setConflictMode] = useState<ConflictResolutionMode>('none');
  const [alternativeEmail, setAlternativeEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);

  const { signIn } = useAuth();
  const { activeCertificateId } = useCertificate();

  // Check for email conflicts when modal opens
  useEffect(() => {
    const checkEmailConflict = async () => {
      if (!isOpen || !userEmail) return;

      setIsCheckingEmail(true);
      setError(null);

      try {
        const emailExists = await checkEmailExists(userEmail);

        if (emailExists) {
          setConflictMode('email_conflict');
          setError('Diese E-Mail-Adresse ist bereits registriert.');
        } else {
          setConflictMode('none');
        }
      } catch (err) {
        console.error('Error checking email existence:', err);
        // If we can't check, proceed with normal flow
        setConflictMode('none');
      } finally {
        setIsCheckingEmail(false);
      }
    };

    checkEmailConflict();
  }, [isOpen, userEmail]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate form
    if (!password || !confirmPassword) {
      setError('Bitte füllen Sie alle Felder aus.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Die Passwörter stimmen nicht überein.');
      return;
    }

    if (password.length < 6) {
      setError('Das Passwort muss mindestens 6 Zeichen lang sein.');
      return;
    }

    try {
      setLoading(true);

      // Since we already checked for email conflicts upfront, we can proceed directly with conversion
      // Use the basic conversion function to avoid double-checking
      const result = await convertAnonymousUser(userEmail, password);

      if (!result.success) {
        setError(result.error || 'Fehler beim Erstellen des Kontos.');
        return;
      }

      // Success
      onSuccess();
    } catch (err: any) {
      console.error('Account conversion error:', err);
      setError('Fehler beim Erstellen des Kontos. Bitte versuchen Sie es erneut.');
    } finally {
      setLoading(false);
    }
  };

  // Handle login with existing account and transfer certificate data
  const handleLoginWithExisting = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!loginPassword) {
      setError('Bitte geben Sie Ihr Passwort ein.');
      return;
    }

    try {
      setLoading(true);

      // Sign in with existing account
      const { data, error } = await signIn(userEmail, loginPassword);

      if (error || !data?.user) {
        setError('Ungültige Anmeldedaten. Bitte überprüfen Sie Ihr Passwort.');
        return;
      }

      // Transfer certificate data to the existing account
      if (activeCertificateId) {
        const transferResult = await transferCertificateToExistingAccount(
          activeCertificateId,
          data.user.id
        );

        if (!transferResult.success) {
          setError(transferResult.error || 'Fehler beim Übertragen der Zertifikatsdaten.');
          return;
        }
      }

      // Success
      onSuccess();
    } catch (err: any) {
      console.error('Login and transfer error:', err);
      setError('Fehler beim Anmelden. Bitte versuchen Sie es erneut.');
    } finally {
      setLoading(false);
    }
  };

  // Handle using alternative email
  const handleAlternativeEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!alternativeEmail || !password || !confirmPassword) {
      setError('Bitte füllen Sie alle Felder aus.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Die Passwörter stimmen nicht überein.');
      return;
    }

    if (password.length < 6) {
      setError('Das Passwort muss mindestens 6 Zeichen lang sein.');
      return;
    }

    try {
      setLoading(true);

      // Try conversion with alternative email
      const result = await convertAnonymousUserWithConflictCheck(alternativeEmail, password);

      if (!result.success) {
        if (result.conflictType === 'email_exists') {
          setError('Diese E-Mail-Adresse ist ebenfalls bereits registriert. Bitte wählen Sie eine andere.');
        } else {
          setError(result.error || 'Fehler beim Erstellen des Kontos.');
        }
        return;
      }

      // Success
      onSuccess();
    } catch (err: any) {
      console.error('Alternative email conversion error:', err);
      setError('Fehler beim Erstellen des Kontos. Bitte versuchen Sie es erneut.');
    } finally {
      setLoading(false);
    }
  };

  // Reset conflict mode and re-check email
  const handleBackToMain = async () => {
    setConflictMode('none');
    setError(null);
    setLoginPassword('');
    setAlternativeEmail('');

    // Re-check email when going back to main
    setIsCheckingEmail(true);
    try {
      const emailExists = await checkEmailExists(userEmail);
      if (emailExists) {
        setConflictMode('email_conflict');
        setError('Diese E-Mail-Adresse ist bereits registriert.');
      } else {
        setConflictMode('none');
      }
    } catch (err) {
      console.error('Error re-checking email existence:', err);
      setConflictMode('none');
    } finally {
      setIsCheckingEmail(false);
    }
  };

  // Reset all state when modal closes
  const handleClose = () => {
    setPassword('');
    setConfirmPassword('');
    setError(null);
    setConflictMode('none');
    setAlternativeEmail('');
    setLoginPassword('');
    setIsCheckingEmail(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">
            {isCheckingEmail ? 'E-Mail wird überprüft...' :
             conflictMode === 'email_conflict' ? 'E-Mail bereits registriert' : 'Konto erstellen'}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isCheckingEmail}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {isCheckingEmail && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            <span className="ml-3 text-gray-600">E-Mail-Adresse wird überprüft...</span>
          </div>
        )}

        {!isCheckingEmail && conflictMode === 'none' && (
          <>
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-700">
                Erstellen Sie ein Passwort für Ihr Konto mit der E-Mail-Adresse{' '}
                <strong>{userEmail}</strong>. So können Sie Ihre Energieausweise später verwalten.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Passwort
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Mindestens 6 Zeichen"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  Passwort bestätigen
                </label>
                <div className="relative">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Passwort wiederholen"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              {error && (
                <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {error}
                </div>
              )}

              <div className="flex justify-end pt-4">
                <button
                  type="submit"
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-400 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  {loading ? 'Konto wird erstellt...' : 'Konto erstellen'}
                </button>
              </div>
            </form>

            <div className="mt-4 text-xs text-gray-500">
              <p>
                Durch das Erstellen eines Kontos können Sie Ihre Energieausweise verwalten,
                den Status einsehen und bei Bedarf erneut herunterladen.
              </p>
            </div>
          </>
        )}

        {!isCheckingEmail && conflictMode === 'email_conflict' && (
          <>
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    E-Mail-Adresse bereits registriert
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>
                      Die E-Mail-Adresse <strong>{userEmail}</strong> ist bereits mit einem Konto verknüpft.
                      Wählen Sie eine der folgenden Optionen:
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="p-4 border border-blue-200 rounded-md bg-blue-50">
                <h4 className="font-medium text-blue-900 mb-2">Option 1: Mit bestehendem Konto anmelden</h4>
                <p className="text-sm text-blue-700 mb-3">
                  Melden Sie sich mit Ihrem bestehenden Konto an. Ihre Zertifikatsdaten werden automatisch übertragen.
                </p>
                <form onSubmit={handleLoginWithExisting} className="space-y-3">
                  <div>
                    <label htmlFor="loginPassword" className="block text-sm font-medium text-gray-700 mb-1">
                      Passwort für {userEmail}
                    </label>
                    <input
                      id="loginPassword"
                      name="loginPassword"
                      type="password"
                      autoComplete="current-password"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Ihr Passwort"
                      value={loginPassword}
                      onChange={(e) => setLoginPassword(e.target.value)}
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:bg-blue-400 disabled:cursor-not-allowed"
                    disabled={loading}
                  >
                    {loading ? 'Anmeldung läuft...' : 'Anmelden und Daten übertragen'}
                  </button>
                </form>
              </div>

              <div className="p-4 border border-green-200 rounded-md bg-green-50">
                <h4 className="font-medium text-green-900 mb-2">Option 2: Andere E-Mail-Adresse verwenden</h4>
                <p className="text-sm text-green-700 mb-3">
                  Verwenden Sie eine andere E-Mail-Adresse für Ihr neues Konto.
                </p>
                <form onSubmit={handleAlternativeEmail} className="space-y-3">
                  <div>
                    <label htmlFor="alternativeEmail" className="block text-sm font-medium text-gray-700 mb-1">
                      Alternative E-Mail-Adresse
                    </label>
                    <input
                      id="alternativeEmail"
                      name="alternativeEmail"
                      type="email"
                      autoComplete="email"
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                      placeholder="<EMAIL>"
                      value={alternativeEmail}
                      onChange={(e) => setAlternativeEmail(e.target.value)}
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-400 disabled:cursor-not-allowed"
                    disabled={loading}
                  >
                    {loading ? 'Konto wird erstellt...' : 'Mit neuer E-Mail registrieren'}
                  </button>
                </form>
              </div>

              {error && (
                <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {error}
                </div>
              )}

              <div className="flex justify-start pt-4">
                <button
                  type="button"
                  onClick={handleBackToMain}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
                  disabled={loading}
                >
                  Zurück
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
